#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam Games List Parser
Parsu<PERSON> stažený MHTML soubor se seznamem Steam her a exportuje do CSV.
"""

import re
import csv
import html
import quopri
from datetime import datetime
from typing import List, Tuple, Optional


def decode_quoted_printable(text: str) -> str:
    """Dekóduje quoted-printable text."""
    try:
        # Nahradíme =\n (soft line breaks) prázdným stringem
        text = re.sub(r'=\r?\n', '', text)
        # Dekódujeme quoted-printable
        decoded_bytes = quopri.decodestring(text.encode('ascii'))
        decoded_text = decoded_bytes.decode('utf-8', errors='ignore')

        # Dodatečné dekódování HTML entit pro lepší výsledek
        decoded_text = html.unescape(decoded_text)

        return decoded_text
    except Exception as e:
        print(f"Chyba p<PERSON><PERSON> quoted-printable: {e}")
        return text


def clean_html_text(text: str) -> str:
    """Vyčistí HTML tagy a entity z textu."""
    # Dekódujeme HTML entity
    text = html.unescape(text)

    # Odstraníme HTML tagy
    text = re.sub(r'<[^>]+>', '', text)

    # Normalizujeme whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def clean_game_name(game_name: str) -> str:
    """Vyčistí název hry od nežádoucích elementů."""
    # Odstraníme text "Odebrat" na začátku
    game_name = re.sub(r'^Odebrat\s+', '', game_name, flags=re.IGNORECASE)

    # Odstraníme další nežádoucí texty
    unwanted_patterns = [
        r'^\s*\[.*?\]\s*',  # [něco] na začátku
        r'\s*\(bundle only\)\s*$',  # (bundle only) na konci
    ]

    for pattern in unwanted_patterns:
        game_name = re.sub(pattern, '', game_name, flags=re.IGNORECASE)

    return game_name.strip()


def parse_date(date_str: str) -> str:
    """Parsuje datum z českého formátu."""
    # Mapování českých měsíců
    czech_months = {
        'led': '01', 'úno': '02', 'bře': '03', 'dub': '04',
        'kvě': '05', 'čvn': '06', 'čvc': '07', 'srp': '08',
        'zář': '09', 'říj': '10', 'lis': '11', 'pro': '12'
    }

    # Vzor pro český datum: "29. kvě. 2025"
    pattern = r'(\d{1,2})\.\s*([a-záčďéěíňóřšťúůýž]+)\.?\s*(\d{4})'
    match = re.search(pattern, date_str, re.IGNORECASE)

    if match:
        day, month_abbr, year = match.groups()
        month_abbr = month_abbr.lower()

        # Najdeme odpovídající měsíc
        for czech, num in czech_months.items():
            if month_abbr.startswith(czech):
                return f"{year}-{num}-{day.zfill(2)}"

    return date_str  # Vrátíme původní, pokud se nepodaří parsovat


def extract_games_from_mhtml(file_path: str) -> List[Tuple[str, str, str]]:
    """Extrahuje seznam her z MHTML souboru."""
    games = []

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Dekódujeme quoted-printable obsah
    content = decode_quoted_printable(content)

    # Najdeme tabulku s hrami - hledáme řádky s license_date_col
    table_pattern = r'<tr[^>]*>.*?<td[^>]*class="license_date_col"[^>]*>(.*?)</td>.*?<td[^>]*>(.*?)</td>.*?<td[^>]*class="license_acquisition_col"[^>]*>(.*?)</td>.*?</tr>'

    matches = re.findall(table_pattern, content, re.DOTALL | re.IGNORECASE)

    for match in matches:
        date_raw, game_raw, acquisition_raw = match

        # Vyčistíme data
        date = clean_html_text(date_raw)
        game_name = clean_html_text(game_raw)
        acquisition = clean_html_text(acquisition_raw)

        # Vyčistíme název hry
        original_name = game_name
        game_name = clean_game_name(game_name)

        # Debug výpis pro první pár her
        if len(games) < 5 and "Odebrat" in original_name:
            print(f"Debug: '{original_name}' -> '{game_name}'")

        # Parsujeme datum
        date_formatted = parse_date(date)

        # Přeskočíme prázdné řádky
        if game_name and game_name.strip():
            games.append((date_formatted, game_name, acquisition))

    return games


def save_to_csv(games: List[Tuple[str, str, str]], output_file: str):
    """Uloží seznam her do CSV souboru."""
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # Hlavička
        writer.writerow(['Datum', 'Název hry', 'Způsob pořízení'])

        # Data
        for game in games:
            writer.writerow(game)


def main():
    """Hlavní funkce."""
    input_file = 'games_list_cy80r6.mhtml'
    output_file = 'steam_games.csv'

    print(f"Parsování souboru: {input_file}")

    try:
        games = extract_games_from_mhtml(input_file)

        if games:
            print(f"Nalezeno {len(games)} her")

            # Seřadíme podle data (nejnovější první)
            games.sort(key=lambda x: x[0], reverse=True)

            save_to_csv(games, output_file)
            print(f"Data uložena do: {output_file}")

            # Zobrazíme prvních 5 her jako ukázku
            print("\nPrvních 5 her:")
            for i, (date, name, acquisition) in enumerate(games[:5], 1):
                print(f"{i}. {date} - {name} ({acquisition})")

        else:
            print("Nebyly nalezeny žádné hry!")

    except Exception as e:
        print(f"Chyba při zpracování: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()