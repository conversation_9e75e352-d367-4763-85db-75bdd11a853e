#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test parseru - ov<PERSON><PERSON><PERSON><PERSON> č<PERSON><PERSON><PERSON> n<PERSON>zvů her.
"""

from parser import clean_game_name

def test_clean_game_name():
    """Test funkce pro čišt<PERSON><PERSON><PERSON> názvů her."""
    test_cases = [
        ("Odebrat HITMAN 3 Free Starter Pack", "HITMAN 3 Free Starter Pack"),
        ("Odebrat Drakensang Online", "Drakensang Online"),
        ("Black Mesa", "Black Mesa"),
        ("Amerzone: The Explorer's Legacy (1999) Limited Free Promotional Package - Mar 2025", "Amerzone: The Explorer's Legacy (1999)"),
        ("DOOM Eternal (Campaign) (BNET)", "DOOM Eternal (Campaign)"),
        ("Frostpunk Expansions Original Soundtrack (bundle only)", "Frostpunk Expansions Original Soundtrack"),
        ("  Odebrat   Test Game  ", "Test Game"),
        ("", ""),
    ]
    
    print("Test čištění n<PERSON>zvů her:")
    print("=" * 60)
    
    all_passed = True
    for i, (input_name, expected) in enumerate(test_cases, 1):
        result = clean_game_name(input_name)
        passed = result == expected
        all_passed = all_passed and passed
        
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{i}. {status}")
        print(f"   Vstup:    '{input_name}'")
        print(f"   Očekáváno: '{expected}'")
        print(f"   Výsledek:  '{result}'")
        print()
    
    print("=" * 60)
    if all_passed:
        print("🎉 Všechny testy prošly!")
    else:
        print("⚠️  Některé testy selhaly!")
    
    return all_passed

if __name__ == "__main__":
    test_clean_game_name()
