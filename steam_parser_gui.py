#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam Games List Parser - GUI verze
Grafické rozhraní pro parsování Steam her s možností výběru sloupců.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from parser import extract_games_from_mhtml, save_to_csv
import csv


class SteamParserGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Steam Games Parser")
        self.root.geometry("600x500")
        
        # Data
        self.games_data = []
        self.input_file = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """Vytvoří uživatelské rozhraní."""
        # Hlavní frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Konfigurace grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Výběr souboru
        ttk.Label(main_frame, text="Vstupní soubor:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        file_frame.columnconfigure(0, weight=1)
        
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var, state="readonly")
        self.file_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(file_frame, text="Procházet...", command=self.browse_file).grid(row=0, column=1)
        
        # Tlačítko pro načtení dat
        ttk.Button(main_frame, text="Načíst data", command=self.load_data).grid(row=1, column=0, columnspan=3, pady=10)
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # Výběr sloupců
        ttk.Label(main_frame, text="Vyberte sloupce pro export:", font=('TkDefaultFont', 10, 'bold')).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
        
        # Checkboxy pro sloupce
        self.columns_frame = ttk.Frame(main_frame)
        self.columns_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        self.column_vars = {}
        columns = [
            ("date", "Datum", True),
            ("name", "Název hry", True),
            ("acquisition", "Způsob pořízení", True)
        ]
        
        for i, (key, label, default) in enumerate(columns):
            var = tk.BooleanVar(value=default)
            self.column_vars[key] = var
            ttk.Checkbutton(self.columns_frame, text=label, variable=var).grid(row=i, column=0, sticky=tk.W, pady=2)
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # Náhled dat
        ttk.Label(main_frame, text="Náhled dat:", font=('TkDefaultFont', 10, 'bold')).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=(0, 5))
        
        # Treeview pro náhled
        self.tree_frame = ttk.Frame(main_frame)
        self.tree_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        self.tree_frame.columnconfigure(0, weight=1)
        self.tree_frame.rowconfigure(0, weight=1)
        
        # Treeview s scrollbary
        self.tree = ttk.Treeview(self.tree_frame, columns=("date", "name", "acquisition"), show="headings", height=10)
        
        # Hlavičky
        self.tree.heading("date", text="Datum")
        self.tree.heading("name", text="Název hry")
        self.tree.heading("acquisition", text="Způsob pořízení")
        
        # Šířky sloupců
        self.tree.column("date", width=100)
        self.tree.column("name", width=300)
        self.tree.column("acquisition", width=150)
        
        # Scrollbary
        v_scrollbar = ttk.Scrollbar(self.tree_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.tree_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout pro treeview a scrollbary
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Konfigurace grid weights
        main_frame.rowconfigure(7, weight=1)
        
        # Informace o počtu her
        self.info_var = tk.StringVar(value="Žádná data načtena")
        ttk.Label(main_frame, textvariable=self.info_var).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=5)
        
        # Tlačítka pro export
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=9, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="Exportovat CSV", command=self.export_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Aktualizovat náhled", command=self.update_preview).pack(side=tk.LEFT, padx=5)
        
        # Nastavení výchozího souboru, pokud existuje
        default_file = "games_list_cy80r6.mhtml"
        if os.path.exists(default_file):
            self.file_var.set(default_file)
            self.input_file = default_file
    
    def browse_file(self):
        """Otevře dialog pro výběr souboru."""
        filename = filedialog.askopenfilename(
            title="Vyberte MHTML soubor se Steam hrami",
            filetypes=[
                ("MHTML soubory", "*.mhtml"),
                ("HTML soubory", "*.html"),
                ("Všechny soubory", "*.*")
            ]
        )
        
        if filename:
            self.file_var.set(filename)
            self.input_file = filename
    
    def load_data(self):
        """Načte data ze souboru."""
        if not self.input_file:
            messagebox.showerror("Chyba", "Nejprve vyberte vstupní soubor!")
            return
        
        if not os.path.exists(self.input_file):
            messagebox.showerror("Chyba", f"Soubor '{self.input_file}' neexistuje!")
            return
        
        # Zobrazení progress dialogu
        progress_window = tk.Toplevel(self.root)
        progress_window.title("Načítání...")
        progress_window.geometry("300x100")
        progress_window.transient(self.root)
        progress_window.grab_set()
        
        # Centrování okna
        progress_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 150,
            self.root.winfo_rooty() + 200
        ))
        
        ttk.Label(progress_window, text="Načítání dat ze souboru...").pack(pady=20)
        progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
        progress_bar.pack(pady=10, padx=20, fill=tk.X)
        progress_bar.start()
        
        def load_thread():
            try:
                self.games_data = extract_games_from_mhtml(self.input_file)
                
                # Aktualizace UI v hlavním vlákně
                self.root.after(0, lambda: self.on_data_loaded(progress_window))
                
            except Exception as e:
                self.root.after(0, lambda: self.on_load_error(progress_window, str(e)))
        
        # Spuštění v separátním vlákně
        threading.Thread(target=load_thread, daemon=True).start()
    
    def on_data_loaded(self, progress_window):
        """Callback po úspěšném načtení dat."""
        progress_window.destroy()
        
        if self.games_data:
            self.info_var.set(f"Načteno {len(self.games_data)} her")
            self.update_preview()
            messagebox.showinfo("Úspěch", f"Úspěšně načteno {len(self.games_data)} her!")
        else:
            self.info_var.set("Žádná data načtena")
            messagebox.showwarning("Varování", "V souboru nebyly nalezeny žádné hry!")
    
    def on_load_error(self, progress_window, error_msg):
        """Callback při chybě načítání."""
        progress_window.destroy()
        messagebox.showerror("Chyba", f"Chyba při načítání dat:\n{error_msg}")
    
    def update_preview(self):
        """Aktualizuje náhled dat podle vybraných sloupců."""
        # Vyčištění treeview
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        if not self.games_data:
            return
        
        # Zobrazení prvních 50 her
        preview_data = self.games_data[:50]
        
        for date, name, acquisition in preview_data:
            # Zkrácení dlouhých názvů pro lepší zobrazení
            display_name = name if len(name) <= 60 else name[:57] + "..."
            
            self.tree.insert("", "end", values=(date, display_name, acquisition))
        
        if len(self.games_data) > 50:
            self.tree.insert("", "end", values=("...", f"... a dalších {len(self.games_data) - 50} her", "..."))
    
    def export_csv(self):
        """Exportuje data do CSV podle vybraných sloupců."""
        if not self.games_data:
            messagebox.showerror("Chyba", "Nejprve načtěte data!")
            return
        
        # Kontrola, zda je vybrán alespoň jeden sloupec
        selected_columns = [key for key, var in self.column_vars.items() if var.get()]
        if not selected_columns:
            messagebox.showerror("Chyba", "Vyberte alespoň jeden sloupec pro export!")
            return
        
        # Dialog pro uložení souboru
        output_file = filedialog.asksaveasfilename(
            title="Uložit CSV soubor",
            defaultextension=".csv",
            filetypes=[
                ("CSV soubory", "*.csv"),
                ("Všechny soubory", "*.*")
            ],
            initialvalue="steam_games.csv"
        )
        
        if not output_file:
            return
        
        try:
            # Příprava dat pro export
            column_mapping = {
                "date": ("Datum", 0),
                "name": ("Název hry", 1),
                "acquisition": ("Způsob pořízení", 2)
            }
            
            # Vytvoření hlavičky
            headers = [column_mapping[col][0] for col in selected_columns]
            
            # Export dat
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
                
                for game in self.games_data:
                    row = [game[column_mapping[col][1]] for col in selected_columns]
                    writer.writerow(row)
            
            messagebox.showinfo("Úspěch", f"Data byla úspěšně exportována do:\n{output_file}")
            
        except Exception as e:
            messagebox.showerror("Chyba", f"Chyba při exportu:\n{str(e)}")


def main():
    """Hlavní funkce."""
    root = tk.Tk()
    app = SteamParserGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
