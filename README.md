# Steam Games List Parser

<PERSON>ás<PERSON>j pro parsování seznamu Steam her ze stažené webové stránky a export do CSV formátu.

## Funkce

- **Parsování MHTML souborů** - Zpracovává stažené Steam stránky s licencemi
- **Čištění dat** - Automaticky odstraňuje nežádoucí elementy (tlačítka "Odebrat", HTML tagy)
- **Flexibilní export** - Možnost výběru sloupců pro export
- **GUI rozhraní** - Jednoduché grafické rozhraní pro snadné použití
- **Podpora češtiny** - Správné parsování českých dat a formátů

## Soubory

- `parser.py` - <PERSON><PERSON><PERSON><PERSON><PERSON> parser (příkazová řádka)
- `steam_parser_gui.py` - GUI verze s možností výběru sloupců
- `games_list_cy80r6.mhtml` - Vstupn<PERSON> soubor se Steam hrami
- `steam_games.csv` - Výstupní CSV soubor

## Použití

### GUI verze (doporučeno)

```bash
python steam_parser_gui.py
```

**Postup:**
1. Spusťte aplikaci
2. Vyberte vstupní MHTML soubor (nebo použijte výchozí)
3. Klikněte na "Načíst data"
4. Vyberte sloupce, které chcete exportovat:
   - ✅ **Datum** - Datum pořízení hry
   - ✅ **Název hry** - Název hry (vyčištěný)
   - ✅ **Způsob pořízení** - Jak byla hra pořízena (Steam, Maloobchod, Zdarma)
5. Zkontrolujte náhled dat
6. Klikněte na "Exportovat CSV" a vyberte umístění souboru

### Příkazová řádka

```bash
python parser.py
```

Automaticky zpracuje `games_list_cy80r6.mhtml` a vytvoří `steam_games.csv`.

## Podporované formáty

### Vstupní soubory
- **MHTML** (.mhtml) - Stažené webové stránky
- **HTML** (.html) - HTML soubory

### Výstupní formáty
- **CSV** - Comma-separated values s UTF-8 kódováním

## Struktura dat

Parser extrahuje následující informace:

| Sloupec | Popis | Příklad |
|---------|-------|---------|
| Datum | Datum pořízení hry | `2025-05-29` |
| Název hry | Vyčištěný název hry | `HITMAN 3 Free Starter Pack` |
| Způsob pořízení | Jak byla hra získána | `Zdarma`, `Obchod služby Steam`, `Maloobchod` |

## Čištění dat

Parser automaticky odstraňuje:

- ❌ Text "Odebrat" (tlačítka pro odstranění)
- ❌ HTML tagy a entity
- ❌ Promotional package texty
- ❌ Battle.net označení
- ❌ Bundle-only označení
- ✅ Normalizuje mezery a formátování

## Příklady výstupu

### Všechny sloupce
```csv
Datum,Název hry,Způsob pořízení
2025-05-29,HITMAN 3 Free Starter Pack,Zdarma
2025-05-29,Dune: Awakening Deluxe Edition,Obchod služby Steam
2025-05-18,Black Mesa,Obchod služby Steam
```

### Pouze názvy her
```csv
Název hry
HITMAN 3 Free Starter Pack
Dune: Awakening Deluxe Edition
Black Mesa
```

### Pouze datum a název
```csv
Datum,Název hry
2025-05-29,HITMAN 3 Free Starter Pack
2025-05-29,Dune: Awakening Deluxe Edition
2025-05-18,Black Mesa
```

## Požadavky

- **Python 3.6+**
- **Standardní knihovny**: `tkinter`, `csv`, `html`, `quopri`, `re`, `datetime`

## Jak získat vstupní soubor

1. Přihlaste se do Steam
2. Jděte na: **Účet** → **Zobrazit historii licencí a aktivací produktů**
3. Uložte stránku jako MHTML soubor (Ctrl+S → "Webová stránka, kompletní")

## Řešení problémů

### "Nebyly nalezeny žádné hry"
- Zkontrolujte, zda je soubor správný MHTML ze Steam stránky
- Ujistěte se, že stránka obsahuje tabulku s hrami

### "Chyba při dekódování"
- Soubor může být poškozen při stahování
- Zkuste stáhnout stránku znovu

### Špatné znaky v názvech
- Parser používá UTF-8 kódování
- Otevřete CSV v aplikaci podporující UTF-8 (např. LibreOffice Calc)

## Statistiky

Z testovacího souboru bylo úspěšně zpracováno:
- **898 her** celkem
- **Rozsah dat**: 2020-2025
- **Typy pořízení**: Zdarma, Steam obchod, Maloobchod, Dárky

## Licence

Tento projekt je poskytován "jak je" pro osobní použití.
